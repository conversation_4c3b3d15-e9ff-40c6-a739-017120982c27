// SPDX-License-Identifier: GPL-3.0-or-later

use crate::app::ContextView;
use crate::client::{Client, ClientSelector};
use crate::cosmic_ext::ActionExt;
use crate::fl;
use crate::page::components::{
    FilteringEngine, FilterConfig, FilterConfigBuilder,
    <PERSON><PERSON><PERSON>ce<PERSON>andler, DebounceConfig,
    TagManager, TagManagerConfig, TagManagerMessage, TagManagerOutput,
    display_path, render_loaded_state
};
use crate::state::LoadedState;
use archive_organizer::api::{File, FileDataSource, ReadingStatus};
use cosmic::iced::Length;
use cosmic::iced::alignment::{Horizontal, Vertical};

use cosmic::iced_widget::list::Content;
use cosmic::widget;
use cosmic::{Apply, Element, Task};
use std::collections::HashSet;


struct Files {
    all_files: Vec<File>,
    visible_files: Content<File>,
}

impl Files {
    fn new(files: Vec<File>) -> Self {
        Self {
            all_files: files.clone(),
            visible_files: Content::with_items(files),
        }
    }

    fn set_visible(&mut self, files: Vec<File>) {
        self.visible_files = Content::with_items(files);
    }

    /// Filter files using the reusable filtering engine
    fn filtered_by(
        mut self,
        filter_config: &FilterConfig,
    ) -> Self {
        let filtered_files = FilteringEngine::filter_files_sync(self.all_files.clone(), filter_config);
        self.set_visible(filtered_files);
        self
    }
}

type FileState = LoadedState<Files>;

impl FileState {
    pub fn view(&self) -> Element<FileListMessage> {
        match self {
            FileState::New => widget::text(fl!("file-list-new")).into(),
            FileState::Loading => widget::text(fl!("file-list-loading")).into(),
            FileState::Failed(error) => {
                widget::text(format!("{}: {}", fl!("generic-error"), error)).into()
            }
            FileState::Loaded(files) => {
                let list = cosmic::iced::widget::list(&files.visible_files, |_index, file| {
                    view_file(file)
                })
                .spacing(10);
                list.apply(widget::scrollable::vertical).into()
            }
        }
    }

    fn set_visible(&mut self, files: Vec<File>) {
        self.unwrap_mut().set_visible(files);
    }
}

pub struct FileList {
    client: Client,
    archive: FileState,
    is_filtering: bool,                   // Track if filtering is in progress
    search_query: String,                 // The search query string
    search_input_id: cosmic::widget::Id,  // Unique ID for focus management
    search_input_is_focussed: bool,       // Flag to indicate search input should be focused
    debounce_handler: DebounceHandler,    // Debounce handler for search
    status_filter: Option<ReadingStatus>, // Optional reading status filter
    allow_tags: HashSet<String>,          // Tags that files must have (whitelist)
    deny_tags: HashSet<String>,           // Tags that files must not have (blacklist)
    allow_tag_manager: TagManager,        // Tag manager for allow tags
    deny_tag_manager: TagManager,         // Tag manager for deny tags
}

#[derive(Debug, Clone)]
pub enum FileListOutput {
    OpenFileDetails(File),
    ToggleContextPage(ClientSelector),
}

#[derive(Debug, Clone)]
pub enum FileListMessage {
    LoadArchive,
    Loaded(Vec<File>),
    LoadingFailed(String),
    SearchChanged(String),
    ClearSearch,
    FilteringComplete(Vec<File>),
    FocusSearchInput,
    DebounceTimeout(u32, String), // (counter, query) - triggers filtering after delay
    StatusFilterChanged(Option<ReadingStatus>),
    ClearStatusFilter,
    // Tag filtering messages using tag managers
    AllowTagManager(TagManagerMessage),
    DenyTagManager(TagManagerMessage),
    ClearAllTagFilters,
    Out(FileListOutput),
}

impl FileList {
    pub fn selector(&self) -> ClientSelector {
        self.client.selector()
    }

    pub fn client(&self) -> &Client {
        &self.client
    }

    /// Attempt to focus the search input using various cosmic framework approaches
    /// This method contains multiple approaches that could work depending on cosmic's API
    fn try_focus_search_input(&self) -> Task<cosmic::Action<FileListMessage>> {
        cosmic::widget::text_input::focus(self.search_input_id.clone())
    }

    /// Start debounce timer - waits for user to stop typing before filtering
    fn start_debounce_timer(
        &mut self,
        query: String,
    ) -> Task<cosmic::Action<FileListMessage>> {
        self.debounce_handler.start_timer(query, FileListMessage::DebounceTimeout)
    }

    /// Helper method to trigger filtering when tags change
    fn trigger_filtering(&mut self) -> Task<cosmic::Action<FileListMessage>> {
        // Invalidate any pending debounce timers
        self.debounce_handler.invalidate();

        // Immediately filter with new tags
        if self.archive.is_loaded() && !self.is_filtering {
            self.is_filtering = true;
            self.start_background_filtering(self.archive.unwrap().all_files.clone())
        } else {
            Task::none()
        }
    }

    /// Start background filtering task (called after debounce timeout)
    fn start_background_filtering(
        &self,
        all_files: Vec<File>,
    ) -> Task<cosmic::Action<FileListMessage>> {
        let filter_config = FilterConfigBuilder::new()
            .with_search_query(self.search_query.clone())
            .with_status_filter(self.status_filter)
            .with_allow_tags(self.allow_tags.clone())
            .with_deny_tags(self.deny_tags.clone())
            .build();

        FilteringEngine::filter_files_async(
            all_files,
            filter_config,
            FileListMessage::FilteringComplete,
        )
    }

    pub fn new(client: Client) -> (Self, Task<cosmic::Action<FileListMessage>>) {
        let allow_tag_config = TagManagerConfig {
            placeholder_text: fl!("file-list-select-tag"),
            add_button_text: fl!("file-list-add-tag"),
            show_existing_tags: true,
            ..Default::default()
        };

        let deny_tag_config = TagManagerConfig {
            placeholder_text: fl!("file-list-select-tag"),
            add_button_text: fl!("file-list-add-tag"),
            show_existing_tags: true,
            ..Default::default()
        };

        (
            Self {
                client: client.clone(),
                archive: FileState::default(),
                search_query: String::new(),
                is_filtering: false,
                search_input_id: cosmic::widget::Id::unique(),
                search_input_is_focussed: false,
                debounce_handler: DebounceHandler::new(DebounceConfig::default()),
                status_filter: None,
                allow_tags: HashSet::new(),
                deny_tags: HashSet::new(),
                allow_tag_manager: TagManager::new(client.clone(), allow_tag_config),
                deny_tag_manager: TagManager::new(client, deny_tag_config),
            },
            Task::batch(vec![
                cosmic::task::message(FileListMessage::LoadArchive),
                cosmic::task::message(FileListMessage::AllowTagManager(TagManagerMessage::LoadAllTags)),
                cosmic::task::message(FileListMessage::DenyTagManager(TagManagerMessage::LoadAllTags)),
                cosmic::task::message(FileListMessage::FocusSearchInput),
            ]),
        )
    }

    pub fn display_name(&self) -> String {
        self.client.display_name()
    }

    pub fn view(&self) -> Element<FileListMessage> {
        let column = widget::column().spacing(10);

        let header_row = widget::row().align_y(Vertical::Center);

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("open-menu-symbolic"))
                .on_press(FileListMessage::Out(FileListOutput::ToggleContextPage(
                    self.client.selector(),
                )))
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let search_input =
            cosmic::widget::text_input(fl!("file-list-search-placeholder"), &self.search_query)
                .id(self.search_input_id.clone())
                .always_active()
                .on_input(FileListMessage::SearchChanged)
                .width(Length::FillPortion(2));

        let header_row = header_row.push(
            search_input
                .apply(widget::container)
                .height(Length::Shrink)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Center),
        );

        let header_row = header_row.push(
            widget::button::icon(widget::icon::from_name("edit-clear-symbolic"))
                .on_press(FileListMessage::ClearSearch)
                .apply(widget::container)
                .width(Length::Shrink)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let header_row = if self.is_filtering {
            // Show filtering indicator in the header
            header_row.push(
                widget::text(fl!("file-list-filtering"))
                    .size(12)
                    .apply(widget::container)
                    .width(Length::Shrink)
                    .height(Length::Shrink)
                    .align_x(Horizontal::Center)
                    .align_y(Vertical::Center),
            )
        } else {
            header_row
        };

        let header_row = header_row.push(
            widget::text(self.client.display_name())
                .apply(widget::container)
                .width(Length::FillPortion(1))
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            header_row
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Shrink)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center),
        );

        let column = column.push(
            self.archive
                .view()
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Fill)
                .align_x(Horizontal::Left)
                .align_y(Vertical::Top),
        );

        column.into()
    }

    pub fn view_context(&self) -> ContextView<FileListMessage> {
        let mut column = widget::column().spacing(10);

        // Reading Status Filter Section
        let status_section = widget::column()
            .spacing(5)
            .push(widget::text(fl!("file-list-filter-by-status")).size(16))
            .push(
                cosmic::iced::widget::pick_list(
                    [
                        ReadingStatus::Unread,
                        ReadingStatus::Reading,
                        ReadingStatus::Read,
                    ],
                    self.status_filter,
                    |status| FileListMessage::StatusFilterChanged(Some(status)),
                )
                .width(Length::Fill)
                .placeholder(fl!("file-list-all-statuses")),
            )
            .push(
                widget::button::standard(fl!("file-list-clear-filter"))
                    .on_press(FileListMessage::ClearStatusFilter)
                    .width(Length::Fill),
            );

        column = column.push(status_section);

        // Add divider
        column = column.push(cosmic::iced_widget::horizontal_rule(1).width(Length::Fill));

        // Tag Filter Section
        let tag_section = widget::column()
            .spacing(5)
            .push(widget::text(fl!("file-list-filter-by-tags")).size(16));

        let tag_section = self.view_tag_filters(tag_section);

        column = column.push(tag_section);

        ContextView {
            title: fl!("file-list-options-title"),
            content: column.into(),
        }
    }

    fn view_tag_filters<'a>(
        &'a self,
        mut column: widget::Column<'a, FileListMessage>,
    ) -> widget::Column<'a, FileListMessage> {
        // Allow Tags Section
        column = column.push(widget::text(fl!("file-list-allow-tags")));
        column = column.push(
            self.allow_tag_manager
                .view(|msg| FileListMessage::AllowTagManager(msg))
        );

        // Add spacing
        column = column.push(widget::Space::with_height(Length::Fixed(10.0)));

        // Deny Tags Section
        column = column.push(widget::text(fl!("file-list-deny-tags")));
        column = column.push(
            self.deny_tag_manager
                .view(|msg| FileListMessage::DenyTagManager(msg))
        );

        // Clear all tag filters button
        if !self.allow_tags.is_empty() || !self.deny_tags.is_empty() {
            column = column.push(
                widget::button::standard(fl!("file-list-clear-all-tag-filters"))
                    .on_press(FileListMessage::ClearAllTagFilters)
                    .width(Length::Fill),
            );
        }

        column
    }



    pub fn update(&mut self, message: FileListMessage) -> Task<cosmic::Action<FileListMessage>> {
        tracing::debug!("received: {message:?}");
        match message {
            FileListMessage::LoadArchive => {
                self.archive = FileState::Loading;
                let client = self.client.clone();
                cosmic::task::future(async move {
                    match client.get_files().await {
                        Ok(files) => FileListMessage::Loaded(files),
                        Err(error) => FileListMessage::LoadingFailed(format!("{error}")),
                    }
                })
            }
            FileListMessage::Loaded(files) => {
                // For initial load, use synchronous filtering since it's typically fast
                let filter_config = FilterConfigBuilder::new()
                    .with_search_query(self.search_query.clone())
                    .with_status_filter(self.status_filter)
                    .with_allow_tags(self.allow_tags.clone())
                    .with_deny_tags(self.deny_tags.clone())
                    .build();

                let files = Files::new(files).filtered_by(&filter_config);
                self.archive = FileState::Loaded(files);
                Task::none()
            }
            FileListMessage::LoadingFailed(error) => {
                self.archive = FileState::Failed(error);
                Task::none()
            }
            FileListMessage::SearchChanged(query) => {
                self.search_query = query.clone();

                // Only start debounce timer if files have been loaded
                if self.archive.is_loaded() {
                    self.start_debounce_timer(query)
                } else {
                    Task::none()
                }
            }
            FileListMessage::ClearSearch => {
                self.search_query.clear();
                // Invalidate any pending debounce timers
                self.debounce_handler.invalidate();

                // Immediately filter to show all files (no debounce needed for clearing)
                if self.archive.is_loaded() && !self.is_filtering {
                    self.is_filtering = true;
                    self.start_background_filtering(self.archive.unwrap().all_files.clone())
                } else {
                    Task::none()
                }
            }
            FileListMessage::FilteringComplete(filtered_files) => {
                self.is_filtering = false;
                self.archive.set_visible(filtered_files);
                // Set flag to focus search input after re-render
                self.search_input_is_focussed = true;
                cosmic::task::message(FileListMessage::FocusSearchInput)
            }

            FileListMessage::DebounceTimeout(counter, query) => {
                // Only proceed if this timeout matches the current counter (not superseded by newer typing)
                if self.archive.is_loaded()
                    && self.debounce_handler.is_valid_counter(counter)
                    && !self.is_filtering
                {
                    // Update search query from the debounced value
                    self.search_query = query;
                    self.is_filtering = true;
                    Task::batch(vec![
                        self.start_background_filtering(self.archive.unwrap().all_files.clone()),
                        cosmic::task::message(FileListMessage::FocusSearchInput),
                    ])
                } else {
                    // This timeout was superseded by newer typing, ignore it
                    Task::none()
                }
            }
            FileListMessage::FocusSearchInput => {
                self.search_input_is_focussed = false;
                // Use the helper method that contains all the focus approaches to try
                self.try_focus_search_input()
            }
            FileListMessage::StatusFilterChanged(status) => {
                self.status_filter = status;
                // Invalidate any pending debounce timers
                self.debounce_handler.invalidate();

                // Immediately filter with new status (no debounce needed for status changes)
                if self.archive.is_loaded() && !self.is_filtering {
                    self.is_filtering = true;
                    self.start_background_filtering(self.archive.unwrap().all_files.clone())
                } else {
                    Task::none()
                }
            }
            FileListMessage::ClearStatusFilter => {
                self.status_filter = None;
                // Invalidate any pending debounce timers
                self.debounce_handler.invalidate();

                // Immediately filter to show all statuses (no debounce needed for clearing)
                if self.archive.is_loaded() && !self.is_filtering {
                    self.is_filtering = true;
                    self.start_background_filtering(self.archive.unwrap().all_files.clone())
                } else {
                    Task::none()
                }
            }
            FileListMessage::AllowTagManager(msg) => {
                let (task, output) = self.allow_tag_manager.update(msg);
                let mapped_task = task.map(|action| action.map(FileListMessage::AllowTagManager));

                if let Some(output) = output {
                    match output {
                        TagManagerOutput::TagAdded(tag) => {
                            self.allow_tags.insert(tag);
                            self.allow_tag_manager.set_existing_tags(self.allow_tags.clone());
                            self.deny_tag_manager.set_existing_tags(self.deny_tags.clone());
                            self.trigger_filtering()
                        }
                        TagManagerOutput::TagRemoved(tag) => {
                            self.allow_tags.remove(&tag);
                            self.allow_tag_manager.set_existing_tags(self.allow_tags.clone());
                            self.deny_tag_manager.set_existing_tags(self.deny_tags.clone());
                            self.trigger_filtering()
                        }
                        TagManagerOutput::LoadTagsRequested => mapped_task,
                    }
                } else {
                    mapped_task
                }
            }
            FileListMessage::DenyTagManager(msg) => {
                let (task, output) = self.deny_tag_manager.update(msg);
                let mapped_task = task.map(|action| action.map(FileListMessage::DenyTagManager));

                if let Some(output) = output {
                    match output {
                        TagManagerOutput::TagAdded(tag) => {
                            self.deny_tags.insert(tag);
                            self.allow_tag_manager.set_existing_tags(self.allow_tags.clone());
                            self.deny_tag_manager.set_existing_tags(self.deny_tags.clone());
                            self.trigger_filtering()
                        }
                        TagManagerOutput::TagRemoved(tag) => {
                            self.deny_tags.remove(&tag);
                            self.allow_tag_manager.set_existing_tags(self.allow_tags.clone());
                            self.deny_tag_manager.set_existing_tags(self.deny_tags.clone());
                            self.trigger_filtering()
                        }
                        TagManagerOutput::LoadTagsRequested => mapped_task,
                    }
                } else {
                    mapped_task
                }
            }
            FileListMessage::ClearAllTagFilters => {
                self.allow_tags.clear();
                self.deny_tags.clear();

                // Update tag managers
                self.allow_tag_manager.set_existing_tags(HashSet::new());
                self.deny_tag_manager.set_existing_tags(HashSet::new());

                self.trigger_filtering()
            }
            FileListMessage::Out(_) => {
                panic!("should be handled by the parent component")
            }
        }
    }
}

fn view_file<'a>(file: &'a File) -> Element<'a, FileListMessage> {
    display_path(&file.path)
        .apply(cosmic::iced_widget::button)
        .on_press(FileListMessage::Out(FileListOutput::OpenFileDetails(
            file.clone(),
        )))
        .into()
}

// SPDX-License-Identifier: GPL-3.0-or-later

//! Reusable tag management component

use crate::client::Client;
use crate::fl;
use crate::state::LoadedState;
use cosmic::iced::Length;
use cosmic::iced::widget::combo_box;
use cosmic::iced_widget::{Column, Row};
use cosmic::widget::{self, text};
use cosmic::{Element, Task};
use std::collections::HashSet;

/// State for managing available tags
pub struct Tags {
    pub all_tags: Vec<String>,
    pub available_tags: combo_box::State<String>,
}

pub type TagsState = LoadedState<Tags>;

/// Configuration for tag manager behavior
#[derive(Clone)]
pub struct TagManagerConfig {
    pub placeholder_text: String,
    pub add_button_text: String,
    pub no_tags_text: String,
    pub all_tags_in_use_text: String,
    pub loading_text: String,
    pub show_existing_tags: bool,
}

impl Default for TagManagerConfig {
    fn default() -> Self {
        Self {
            placeholder_text: fl!("tag-manager-select-tag"),
            add_button_text: fl!("tag-manager-add"),
            no_tags_text: fl!("tag-manager-no-tags"),
            all_tags_in_use_text: fl!("tag-manager-all-tags-in-use"),
            loading_text: fl!("tag-manager-loading"),
            show_existing_tags: true,
        }
    }
}

/// Messages for tag manager operations
#[derive(Debug, Clone)]
pub enum TagManagerMessage {
    LoadAllTags,
    AllTagsLoaded(Result<Vec<String>, String>),
    UpdateNewTag(String),
    AddTag,
    RemoveTag(String),
}

/// Output messages from tag manager
#[derive(Debug, Clone)]
pub enum TagManagerOutput {
    TagAdded(String),
    TagRemoved(String),
    LoadTagsRequested,
}

/// Reusable tag manager component
pub struct TagManager {
    client: Client,
    tags_state: TagsState,
    new_tag: String,
    existing_tags: HashSet<String>,
    config: TagManagerConfig,
}

impl TagManager {
    pub fn new(client: Client, config: TagManagerConfig) -> Self {
        Self {
            client,
            tags_state: TagsState::default(),
            new_tag: String::new(),
            existing_tags: HashSet::new(),
            config,
        }
    }

    pub fn with_existing_tags(mut self, existing_tags: HashSet<String>) -> Self {
        self.existing_tags = existing_tags;
        self
    }

    pub fn set_existing_tags(&mut self, existing_tags: HashSet<String>) {
        self.existing_tags = existing_tags;
        self.update_available_tags();
    }

    pub fn view<'a, M>(&'a self, map_message: impl Fn(TagManagerMessage) -> M + 'a) -> Element<'a, M> {
        let mut column = Column::new().spacing(10);

        // Show existing tags if configured
        if self.config.show_existing_tags && !self.existing_tags.is_empty() {
            let tags_row = self.existing_tags
                .iter()
                .fold(Row::new().spacing(5), |row, tag| {
                    row.push(
                        widget::button::standard(format!("✕ {}", tag))
                            .on_press(map_message(TagManagerMessage::RemoveTag(tag.clone())))
                    )
                });
            column = column.push(tags_row);
        }

        // Add tag input section
        column = column.push(self.view_tag_input(map_message));

        column.into()
    }

    fn view_tag_input<'a, M>(&'a self, map_message: impl Fn(TagManagerMessage) -> M + 'a) -> Element<'a, M> {
        match &self.tags_state {
            TagsState::Loaded(Tags { available_tags, .. }) => {
                // Check if there are any tags available that aren't already in use
                let has_available_tags = available_tags.options().iter()
                    .any(|tag| !self.existing_tags.contains(tag));

                if !has_available_tags {
                    text(&self.config.all_tags_in_use_text).into()
                } else {
                    let combo = combo_box(
                        available_tags,
                        &self.config.placeholder_text,
                        Some(&self.new_tag),
                        |tag| map_message(TagManagerMessage::UpdateNewTag(tag)),
                    )
                    .width(Length::Fill);

                    let add_button = widget::button::standard(&self.config.add_button_text)
                        .on_press(map_message(TagManagerMessage::AddTag))
                        .width(Length::Shrink);

                    Row::new().push(combo).push(add_button).spacing(5).into()
                }
            }
            TagsState::Loading => text(&self.config.loading_text).into(),
            TagsState::Failed(_) => text("Failed to load tags").into(),
            TagsState::New => text(&self.config.loading_text).into(),
        }
    }

    pub fn update(&mut self, message: TagManagerMessage) -> (Task<cosmic::Action<TagManagerMessage>>, Option<TagManagerOutput>) {
        match message {
            TagManagerMessage::LoadAllTags => {
                self.tags_state = TagsState::Loading;
                let client = self.client.clone();
                let task = cosmic::task::future(async move {
                    match client.get_files_tags().await {
                        Ok(tags) => TagManagerMessage::AllTagsLoaded(Ok(tags)),
                        Err(err) => TagManagerMessage::AllTagsLoaded(Err(format!("{}", err))),
                    }
                });
                (task, Some(TagManagerOutput::LoadTagsRequested))
            }
            TagManagerMessage::AllTagsLoaded(result) => {
                match result {
                    Ok(tags) => {
                        self.update_available_tags_with_list(tags);
                    }
                    Err(err) => {
                        tracing::warn!("Failed to load tags: {}", &err);
                        self.tags_state = TagsState::Failed(err);
                    }
                }
                (Task::none(), None)
            }
            TagManagerMessage::UpdateNewTag(tag) => {
                self.new_tag = tag;
                (Task::none(), None)
            }
            TagManagerMessage::AddTag => {
                if !self.new_tag.trim().is_empty() && !self.existing_tags.contains(&self.new_tag) {
                    let tag = self.new_tag.clone();
                    self.new_tag.clear();
                    (Task::none(), Some(TagManagerOutput::TagAdded(tag)))
                } else {
                    (Task::none(), None)
                }
            }
            TagManagerMessage::RemoveTag(tag) => {
                (Task::none(), Some(TagManagerOutput::TagRemoved(tag)))
            }
        }
    }

    fn update_available_tags(&mut self) {
        if let TagsState::Loaded(Tags { all_tags, .. }) = &self.tags_state {
            self.update_available_tags_with_list(all_tags.clone());
        }
    }

    fn update_available_tags_with_list(&mut self, all_tags: Vec<String>) {
        let available_tags: Vec<String> = all_tags
            .iter()
            .filter(|tag| !self.existing_tags.contains(*tag))
            .cloned()
            .collect();

        let available_tags_state = combo_box::State::new(available_tags);
        self.tags_state = TagsState::Loaded(Tags {
            all_tags,
            available_tags: available_tags_state,
        });
    }
}

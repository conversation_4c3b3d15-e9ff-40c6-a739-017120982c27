// SPDX-License-Identifier: GPL-3.0-or-later

//! Reusable view components for LoadedState

use crate::fl;
use crate::state::LoadedState;
use cosmic::iced::Length;
use cosmic::iced::alignment::{Horizontal, Vertical};
use cosmic::widget;
use cosmic::{Apply, Element};

/// Configuration for LoadedState view rendering
pub struct LoadedStateViewConfig {
    pub new_text: String,
    pub loading_text: String,
    pub error_prefix: String,
}

impl Default for LoadedStateViewConfig {
    fn default() -> Self {
        Self {
            new_text: fl!("loaded-state-new"),
            loading_text: fl!("loaded-state-loading"),
            error_prefix: fl!("loaded-state-error"),
        }
    }
}

/// Trait for rendering LoadedState with custom content
pub trait LoadedStateView<T> {
    /// Render the LoadedState with a custom view function for the loaded content
    fn view_with_config<'a, M>(
        &'a self,
        config: &LoadedStateViewConfig,
        loaded_view: impl Fn(&'a T) -> Element<'a, M>,
    ) -> Element<'a, M>;

    /// Render the LoadedState with default configuration
    fn view_with<'a, M>(
        &'a self,
        loaded_view: impl Fn(&'a T) -> Element<'a, M>,
    ) -> Element<'a, M> {
        self.view_with_config(&LoadedStateViewConfig::default(), loaded_view)
    }

    /// Render a simple loading state view with spinner
    fn view_loading_with_spinner<'a, M>(&'a self, config: &LoadedStateViewConfig) -> Element<'a, M>;

    /// Render an error state with retry button
    fn view_error_with_retry<'a, M>(
        &'a self,
        config: &LoadedStateViewConfig,
        retry_message: M,
    ) -> Element<'a, M>
    where
        M: Clone;
}

impl<T> LoadedStateView<T> for LoadedState<T> {
    fn view_with_config<'a, M>(
        &'a self,
        config: &LoadedStateViewConfig,
        loaded_view: impl Fn(&'a T) -> Element<'a, M>,
    ) -> Element<'a, M> {
        match self {
            LoadedState::New => create_centered_text(&config.new_text),
            LoadedState::Loading => create_centered_text(&config.loading_text),
            LoadedState::Failed(error) => {
                create_centered_text(&format!("{}: {}", config.error_prefix, error))
            }
            LoadedState::Loaded(data) => loaded_view(data),
        }
    }

    fn view_loading_with_spinner<'a, M>(&'a self, config: &LoadedStateViewConfig) -> Element<'a, M> {
        match self {
            LoadedState::Loading => {
                // TODO: Add actual spinner when cosmic supports it
                create_centered_text(&config.loading_text)
            }
            _ => self.view_with_config(config, |_| {
                widget::text("Unexpected state").into()
            }),
        }
    }

    fn view_error_with_retry<'a, M>(
        &'a self,
        config: &LoadedStateViewConfig,
        retry_message: M,
    ) -> Element<'a, M>
    where
        M: Clone,
    {
        match self {
            LoadedState::Failed(error) => {
                cosmic::iced_widget::Column::with_children(vec![
                    widget::text(format!("{}: {}", config.error_prefix, error)).into(),
                    widget::button::standard(fl!("loaded-state-retry"))
                        .on_press(retry_message)
                        .into(),
                ])
                .spacing(10)
                .apply(widget::container)
                .width(Length::Fill)
                .height(Length::Fill)
                .align_x(Horizontal::Center)
                .align_y(Vertical::Center)
                .into()
            }
            _ => self.view_with_config(config, |_| {
                widget::text("Unexpected state").into()
            }),
        }
    }
}

/// Create centered text element
fn create_centered_text<M>(text: &str) -> Element<M> {
    widget::text(text)
        .apply(widget::container)
        .width(Length::Fill)
        .height(Length::Fill)
        .align_x(Horizontal::Center)
        .align_y(Vertical::Center)
        .into()
}

/// Create a loading indicator with optional message
pub fn create_loading_indicator<M>(message: Option<&str>) -> Element<M> {
    let text = message.unwrap_or(&fl!("loaded-state-loading"));
    // TODO: Add spinner animation when cosmic supports it
    create_centered_text(text)
}

/// Create an error display with optional retry functionality
pub fn create_error_display<M>(
    error: &str,
    retry_message: Option<M>,
) -> Element<M>
where
    M: Clone,
{
    let mut column = cosmic::iced_widget::Column::new()
        .spacing(10)
        .push(widget::text(format!("{}: {}", fl!("loaded-state-error"), error)));

    if let Some(retry_msg) = retry_message {
        column = column.push(
            widget::button::standard(fl!("loaded-state-retry"))
                .on_press(retry_msg)
        );
    }

    column
        .apply(widget::container)
        .width(Length::Fill)
        .height(Length::Fill)
        .align_x(Horizontal::Center)
        .align_y(Vertical::Center)
        .into()
}

/// Helper trait for common LoadedState operations
pub trait LoadedStateExt<T> {
    /// Map the loaded content while preserving other states
    fn map_loaded<U, F>(self, f: F) -> LoadedState<U>
    where
        F: FnOnce(T) -> U;

    /// Convert to Option, returning Some only if loaded
    fn to_option(self) -> Option<T>;

    /// Check if the state contains data
    fn has_data(&self) -> bool;
}

impl<T> LoadedStateExt<T> for LoadedState<T> {
    fn map_loaded<U, F>(self, f: F) -> LoadedState<U>
    where
        F: FnOnce(T) -> U,
    {
        match self {
            LoadedState::New => LoadedState::New,
            LoadedState::Loading => LoadedState::Loading,
            LoadedState::Failed(error) => LoadedState::Failed(error),
            LoadedState::Loaded(data) => LoadedState::Loaded(f(data)),
        }
    }

    fn to_option(self) -> Option<T> {
        match self {
            LoadedState::Loaded(data) => Some(data),
            _ => None,
        }
    }

    fn has_data(&self) -> bool {
        matches!(self, LoadedState::Loaded(_))
    }
}

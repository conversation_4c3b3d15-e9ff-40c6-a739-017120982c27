// SPDX-License-Identifier: GPL-3.0-or-later

//! Reusable filtering engine for files

use archive_organizer::api::{File, ReadingStatus};
use cosmic::Task;
use std::collections::HashSet;

/// Configuration for file filtering
#[derive(Clone, Debug)]
pub struct FilterConfig {
    pub search_query: String,
    pub status_filter: Option<ReadingStatus>,
    pub allow_tags: HashSet<String>,
    pub deny_tags: HashSet<String>,
}

impl Default for FilterConfig {
    fn default() -> Self {
        Self {
            search_query: String::new(),
            status_filter: None,
            allow_tags: HashSet::new(),
            deny_tags: HashSet::new(),
        }
    }
}

/// Filtering engine for files
pub struct FilteringEngine;

impl FilteringEngine {
    /// Filter files synchronously (for initial load)
    pub fn filter_files_sync(files: Vec<File>, config: &FilterConfig) -> Vec<File> {
        files
            .into_iter()
            .filter(|file| Self::matches_filter(file, config))
            .collect()
    }

    /// Filter files asynchronously (for background filtering)
    pub fn filter_files_async<M>(
        files: Vec<File>,
        config: FilterConfig,
        result_mapper: impl Fn(Vec<File>) -> M + Send + 'static,
    ) -> Task<cosmic::Action<M>>
    where
        M: Send + 'static,
    {
        cosmic::task::future(async move {
            let filtered_files = files
                .into_iter()
                .filter(|file| Self::matches_filter(file, &config))
                .collect();

            result_mapper(filtered_files)
        })
    }

    /// Check if a file matches the filter criteria
    fn matches_filter(file: &File, config: &FilterConfig) -> bool {
        // Filter by search query
        let matches_search = if config.search_query.is_empty() {
            true
        } else {
            let query = config.search_query.to_lowercase();
            let path_lower = file.path.to_lowercase();
            let tags_lower = file.tags.join(" ").to_lowercase();
            path_lower.contains(&query) || tags_lower.contains(&query)
        };

        // Filter by reading status
        let matches_status = config.status_filter.map_or(true, |status| file.status == status);

        // Filter by allowed tags (file must have ALL allowed tags)
        let matches_allow_tags = config.allow_tags.is_empty() 
            || config.allow_tags.iter().all(|tag| file.tags.contains(tag));

        // Filter by denied tags (file must have NONE of the denied tags)
        let matches_deny_tags = config.deny_tags.is_empty() 
            || !file.tags.iter().any(|tag| config.deny_tags.contains(tag));

        matches_search && matches_status && matches_allow_tags && matches_deny_tags
    }
}

/// Builder for creating filter configurations
pub struct FilterConfigBuilder {
    config: FilterConfig,
}

impl FilterConfigBuilder {
    pub fn new() -> Self {
        Self {
            config: FilterConfig::default(),
        }
    }

    pub fn with_search_query(mut self, query: String) -> Self {
        self.config.search_query = query;
        self
    }

    pub fn with_status_filter(mut self, status: Option<ReadingStatus>) -> Self {
        self.config.status_filter = status;
        self
    }

    pub fn with_allow_tags(mut self, tags: HashSet<String>) -> Self {
        self.config.allow_tags = tags;
        self
    }

    pub fn with_deny_tags(mut self, tags: HashSet<String>) -> Self {
        self.config.deny_tags = tags;
        self
    }

    pub fn build(self) -> FilterConfig {
        self.config
    }
}

impl Default for FilterConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use archive_organizer::api::ReadingStatus;

    fn create_test_file(path: &str, tags: Vec<&str>, status: ReadingStatus) -> File {
        File {
            id: 1,
            path: path.to_string(),
            tags: tags.into_iter().map(|s| s.to_string()).collect(),
            status,
            type_: "pdf".to_string(),
            size: 1000,
            fingerprint: "test".to_string(),
        }
    }

    #[test]
    fn test_search_query_filtering() {
        let files = vec![
            create_test_file("document.pdf", vec![], ReadingStatus::Unread),
            create_test_file("book.epub", vec![], ReadingStatus::Unread),
        ];

        let config = FilterConfigBuilder::new()
            .with_search_query("document".to_string())
            .build();

        let filtered = FilteringEngine::filter_files_sync(files, &config);
        assert_eq!(filtered.len(), 1);
        assert!(filtered[0].path.contains("document"));
    }

    #[test]
    fn test_tag_filtering() {
        let files = vec![
            create_test_file("file1.pdf", vec!["fiction", "novel"], ReadingStatus::Unread),
            create_test_file("file2.pdf", vec!["non-fiction"], ReadingStatus::Unread),
            create_test_file("file3.pdf", vec!["fiction", "short-story"], ReadingStatus::Unread),
        ];

        let mut allow_tags = HashSet::new();
        allow_tags.insert("fiction".to_string());

        let config = FilterConfigBuilder::new()
            .with_allow_tags(allow_tags)
            .build();

        let filtered = FilteringEngine::filter_files_sync(files, &config);
        assert_eq!(filtered.len(), 2);
    }

    #[test]
    fn test_status_filtering() {
        let files = vec![
            create_test_file("file1.pdf", vec![], ReadingStatus::Unread),
            create_test_file("file2.pdf", vec![], ReadingStatus::Read),
            create_test_file("file3.pdf", vec![], ReadingStatus::Reading),
        ];

        let config = FilterConfigBuilder::new()
            .with_status_filter(Some(ReadingStatus::Read))
            .build();

        let filtered = FilteringEngine::filter_files_sync(files, &config);
        assert_eq!(filtered.len(), 1);
        assert_eq!(filtered[0].status, ReadingStatus::Read);
    }
}

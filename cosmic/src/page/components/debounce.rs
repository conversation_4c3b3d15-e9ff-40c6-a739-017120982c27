// SPDX-License-Identifier: GPL-3.0-or-later

//! Reusable debouncing functionality

use cosmic::Task;
use std::time::Duration;

/// Configuration for debounce behavior
#[derive(Clone, Debug)]
pub struct DebounceConfig {
    pub delay: Duration,
}

impl Default for DebounceConfig {
    fn default() -> Self {
        Self {
            delay: Duration::from_millis(250),
        }
    }
}

/// Debounce handler for managing delayed operations
pub struct Debounce<PERSON>and<PERSON> {
    counter: u32,
    config: DebounceConfig,
}

impl DebounceHandler {
    pub fn new(config: DebounceConfig) -> Self {
        Self {
            counter: 0,
            config,
        }
    }

    /// Increment the counter to invalidate previous timers
    pub fn invalidate(&mut self) {
        self.counter += 1;
    }

    /// Get the current counter value
    pub fn current_counter(&self) -> u32 {
        self.counter
    }

    /// Start a debounce timer with the current counter
    pub fn start_timer<M, T>(
        &mut self,
        data: T,
        message_fn: impl Fn(u32, T) -> M + Send + 'static,
    ) -> Task<cosmic::Action<M>>
    where
        M: Send + 'static,
        T: Send + 'static,
    {
        self.counter += 1;
        let counter = self.counter;
        let delay = self.config.delay;

        cosmic::task::future(async move {
            tokio::time::sleep(delay).await;
            message_fn(counter, data)
        })
    }

    /// Check if a counter is still valid (not superseded by newer operations)
    pub fn is_valid_counter(&self, counter: u32) -> bool {
        counter == self.counter
    }
}

/// Trait for components that support debounced operations
pub trait Debounceable {
    type Data: Send + 'static;
    type Message: Send + 'static;

    /// Start a debounced operation
    fn start_debounced_operation(
        &mut self,
        data: Self::Data,
    ) -> Task<cosmic::Action<Self::Message>>;

    /// Handle a debounced timeout
    fn handle_debounced_timeout(
        &mut self,
        counter: u32,
        data: Self::Data,
    ) -> Task<cosmic::Action<Self::Message>>;
}

/// Helper function to create a debounced task
pub fn create_debounced_task<M, T>(
    data: T,
    delay: Duration,
    message_fn: impl Fn(T) -> M + Send + 'static,
) -> Task<cosmic::Action<M>>
where
    M: Send + 'static,
    T: Send + 'static,
{
    cosmic::task::future(async move {
        tokio::time::sleep(delay).await;
        message_fn(data)
    })
}

/// Helper function to create a debounced task with counter validation
pub fn create_debounced_task_with_counter<M, T>(
    counter: u32,
    data: T,
    delay: Duration,
    message_fn: impl Fn(u32, T) -> M + Send + 'static,
) -> Task<cosmic::Action<M>>
where
    M: Send + 'static,
    T: Send + 'static,
{
    cosmic::task::future(async move {
        tokio::time::sleep(delay).await;
        message_fn(counter, data)
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_debounce_handler_counter() {
        let mut handler = DebounceHandler::new(DebounceConfig::default());
        
        let initial_counter = handler.current_counter();
        assert_eq!(initial_counter, 0);
        
        handler.invalidate();
        assert_eq!(handler.current_counter(), 1);
        assert!(!handler.is_valid_counter(initial_counter));
        assert!(handler.is_valid_counter(1));
    }

    #[test]
    fn test_debounce_config_default() {
        let config = DebounceConfig::default();
        assert_eq!(config.delay, Duration::from_millis(250));
    }

    #[test]
    fn test_debounce_handler_new() {
        let config = DebounceConfig {
            delay: Duration::from_millis(500),
        };
        let handler = DebounceHandler::new(config.clone());
        
        assert_eq!(handler.counter, 0);
        assert_eq!(handler.config.delay, config.delay);
    }
}

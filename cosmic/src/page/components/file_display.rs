// SPDX-License-Identifier: GPL-3.0-or-later

//! Reusable file display components

use cosmic::iced::Length;
use cosmic::widget;
use cosmic::{Apply, Element};
use std::borrow::Cow;
use std::path::Path;

/// Helper function to create a row with a label and value
pub fn row_with_label<'a, M>(
    label: impl Into<Cow<'a, str>> + 'a,
    value: impl Into<Cow<'a, str>> + 'a,
) -> Element<'a, M> {
    cosmic::iced_widget::Row::with_children(vec![
        widget::text(label).width(Length::FillPortion(1)).into(),
        widget::text(value).width(Length::FillPortion(3)).into(),
    ])
    .spacing(10)
    .padding(5)
    .into()
}

/// Display a file path with filename and directory separated
pub fn display_path<'a, M>(path: &'a str) -> Element<'a, M> {
    let path: &Path = path.as_ref();
    let directory = format!("{}", path.parent().unwrap_or_else(|| Path::new("")).display());
    let filename = path.file_name().unwrap_or_default();
    
    cosmic::iced_widget::column![
        widget::text(format!("{}", filename.to_string_lossy())),
        widget::text(directory).size(11),
    ]
    .spacing(5)
    .apply(widget::container)
    .width(Length::Fill)
    .into()
}

/// Extract filename and folder components from a path
pub fn extract_path_components(path: &str) -> (String, String) {
    let path = Path::new(path);
    
    let filename = path
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("Unknown")
        .to_string();

    let folder = path
        .parent()
        .and_then(|parent| parent.to_str())
        .unwrap_or("")
        .to_string();

    (filename, folder)
}

/// Extract filename without extension
pub fn extract_filename_without_extension(path: &str) -> String {
    let path = Path::new(path);
    path.file_stem()
        .and_then(|name| name.to_str())
        .unwrap_or("Unknown")
        .to_string()
}

/// Create a section container with standard styling
pub fn create_section<'a, M>(
    title: impl Into<Cow<'a, str>>,
    content: Element<'a, M>,
) -> Element<'a, M> {
    cosmic::iced_widget::Column::with_children(vec![
        widget::text(title).size(20).into(),
        content.apply(cosmic::widget::container).padding(10).into(),
    ])
    .into()
}

/// Create a detail section with multiple rows
pub fn create_detail_section<'a, M>(
    title: impl Into<Cow<'a, str>>,
    rows: Vec<Element<'a, M>>,
) -> Element<'a, M> {
    let content = cosmic::iced_widget::Column::with_children(rows);
    create_section(title, content.into())
}

/// Format file size in a human-readable way
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_path_components() {
        let (filename, folder) = extract_path_components("/home/<USER>/documents/file.pdf");
        assert_eq!(filename, "file.pdf");
        assert_eq!(folder, "/home/<USER>/documents");
    }

    #[test]
    fn test_extract_filename_without_extension() {
        let filename = extract_filename_without_extension("/home/<USER>/documents/file.pdf");
        assert_eq!(filename, "file");
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
        assert_eq!(format_file_size(1073741824), "1.0 GB");
    }

    #[test]
    fn test_extract_path_components_root() {
        let (filename, folder) = extract_path_components("file.pdf");
        assert_eq!(filename, "file.pdf");
        assert_eq!(folder, "");
    }

    #[test]
    fn test_extract_path_components_no_extension() {
        let (filename, folder) = extract_path_components("/home/<USER>/documents/README");
        assert_eq!(filename, "README");
        assert_eq!(folder, "/home/<USER>/documents");
    }
}

app-title = Archive Organizer Cosmic
about = About
view = View
welcome = Welcome to Archive Organizer Cosmic! ✨
page-id = Page { $num }
git-description = Git commit {$hash} on {$date}
unknown-remote = ⚠ Unknown Remote: { $url }

# Generic messages
generic-error = 🛑 Error: { $error }

# File list states
file-list-new = New
file-list-loading = Loading
file-list-search-placeholder = Search files...
file-list-filtering = Filtering...
file-list-options-title = File List Options
file-list-filter-by-status = Filter by Reading Status
file-list-all-statuses = All statuses
file-list-clear-filter = Clear Filter
file-list-filter-by-tags = Filter by Tags
file-list-allow-tags = Include Tags (must have ALL)
file-list-deny-tags = Exclude Tags (must have NONE)
file-list-select-tag = Select a tag
file-list-add-tag = Add
file-list-clear-all-tag-filters = Clear All Tag Filters
file-list-all-tags-in-use = All tags are already in use

# File details
file-details-unknown = Unknown
file-details-basic-info = Basic Information
file-details-folder = Folder
file-details-filename = Filename
file-details-type = Type
file-details-size = Size
file-details-status = Status
file-details-technical = Technical Details
file-details-id = ID
file-details-full-path = Full Path
file-details-fingerprint = Fingerprint
file-details-tags = Tags
file-details-no-tags = No tags
file-details-select-tag = Select a tag
file-details-add = Add
file-details-loading-tags = Loading tags...
file-details-size-bytes = { $size } bytes
file-details-select-status = Select status

# Page messages
page-not-found = ⚠ Not found

# Language menu
language = Language
language-english = English
language-dutch = Nederlands
language-french = Français